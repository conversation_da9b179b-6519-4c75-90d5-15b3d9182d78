const jwt = require('jsonwebtoken');
const config = require('../config');
const { USER_ROLES, AUTH_TYPES } = require('../graphql/permissions/constants');
const logger = require('../helpers/logger');

/**
 * 根据角色获取权限列表
 */
function getPermissionsForRole(userType) {
  const permissions = {
    [USER_ROLES.ADMIN]: ['*'], // 管理员拥有所有权限
    [USER_ROLES.RESTAURANT]: [
      'restaurant:*',
      'order:read',
      'order:update',
      'food:*',
      'category:*',
      'dashboard:read'
    ],
    [USER_ROLES.CUSTOMER]: [
      'profile:*',
      'order:create',
      'order:read',
      'restaurant:read',
      'food:read'
    ],
    [USER_ROLES.WHATSAPP_CUSTOMER]: [
      'profile:*',
      'order:create',
      'order:read',
      'restaurant:read',
      'food:read',
      'address:*'
    ],
    [USER_ROLES.RIDER]: [
      'order:read',
      'order:update:status'
    ],
    [USER_ROLES.VENDOR]: [
      'restaurant:*',
      'order:read',
      'order:update',
      'food:*',
      'category:*',
      'dashboard:read',
      'profile:*'
    ]
  };

  return permissions[userType] || [];
}

/**
 * 增强的JWT认证中间件
 * 返回认证信息对象，支持更细粒度的权限检查
 */
module.exports = (req, res, next) => {
  const authHeader = req.get('Authorization');

  // 没有Authorization头
  if (!authHeader) {
    return {
      isAuth: false,
      authType: null
    };
  }

  // 检查Authorization格式
  const authParts = authHeader.split(' ');
  if (authParts.length !== 2 || authParts[0] !== 'Bearer') {
    logger.warn(`无效的Authorization头格式: ${authHeader}`);
    return {
      isAuth: false,
      authType: null
    };
  }

  const token = authParts[1];
  if (!token || token === '') {
    return {
      isAuth: false,
      authType: null
    };
  }

  let decodedToken;
  try {
    decodedToken = jwt.verify(token, config.JWT_SECRET);
  } catch (err) {
    logger.warn(`JWT验证失败: ${err.message}, Token: ${token.substring(0, 20)}...`);
    return {
      isAuth: false,
      authType: null
    };
  }

  if (!decodedToken) {
    return {
      isAuth: false,
      authType: null
    };
  }

  const userType = decodedToken.userType || USER_ROLES.CUSTOMER;

  // 记录认证成功日志
  logger.debug(`用户认证成功: ID=${decodedToken.userId}, 类型=${userType}, 餐厅=${decodedToken.restaurantId || 'N/A'}`);

  // 返回增强的认证信息
  return {
    isAuth: true,
    userId: decodedToken.userId,
    userType: userType,
    restaurantId: decodedToken.restaurantId,
    authType: AUTH_TYPES.JWT,
    permissions: getPermissionsForRole(userType),
    tokenIssuedAt: decodedToken.iat,
    tokenExpiresAt: decodedToken.exp
  };
};
